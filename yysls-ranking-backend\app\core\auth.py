"""
认证核心模块

提供JWT认证中间件、权限装饰器等认证相关功能
"""
from typing import Optional, List, Callable, Any
from functools import wraps
import logging

from fastapi import HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.user_service import UserService
from app.utils.security import verify_token
from app.models.user import User, UserRole

logger = logging.getLogger(__name__)

# HTTP Bearer 安全方案
security = HTTPBearer()
user_service = UserService()

# Token黑名单（生产环境应使用Redis）
_token_blacklist: set = set()


class AuthenticationError(HTTPException):
    """认证错误异常"""
    def __init__(self, detail: str = "认证失败"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class PermissionError(HTTPException):
    """权限错误异常"""
    def __init__(self, detail: str = "权限不足"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> int:
    """
    获取当前用户ID
    
    Args:
        credentials: HTTP Bearer 凭证
        
    Returns:
        用户ID
        
    Raises:
        AuthenticationError: 认证失败时抛出
    """
    try:
        token = credentials.credentials
        
        # 检查token是否在黑名单中
        if token in _token_blacklist:
            raise AuthenticationError("令牌已失效")
        
        # 验证token
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        if not user_id:
            raise AuthenticationError("令牌格式无效")
        
        return int(user_id)
        
    except ValueError:
        raise AuthenticationError("用户ID格式无效")
    except Exception as e:
        logger.error(f"获取当前用户ID失败: {str(e)}")
        raise AuthenticationError("认证失败")


async def get_current_user(
    user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> User:
    """
    获取当前用户对象
    
    Args:
        user_id: 用户ID
        db: 数据库会话
        
    Returns:
        用户对象
        
    Raises:
        AuthenticationError: 用户不存在或已被禁用时抛出
    """
    try:
        user = user_service.get(db, user_id)

        if not user:
            raise AuthenticationError("用户不存在")

        if not user.is_active:
            raise AuthenticationError("用户账户已被禁用")

        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前用户失败 user_id={user_id}: {str(e)}")
        raise AuthenticationError("获取用户信息失败")


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前活跃用户（已验证的用户）
    
    Args:
        current_user: 当前用户
        
    Returns:
        活跃用户对象
        
    Raises:
        AuthenticationError: 用户未验证时抛出
    """
    if not current_user.is_verified:
        raise AuthenticationError("用户账户未验证")
    
    return current_user


def require_roles(*allowed_roles: UserRole):
    """
    权限装饰器：要求用户具有指定角色之一
    
    Args:
        allowed_roles: 允许的用户角色列表
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user参数
            current_user = kwargs.get('current_user')
            
            if not current_user:
                raise AuthenticationError("缺少用户认证信息")
            
            if current_user.role not in allowed_roles:
                raise PermissionError(f"需要以下角色之一: {', '.join([role.value for role in allowed_roles])}")
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_admin(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    管理员权限依赖
    
    Args:
        current_user: 当前用户
        
    Returns:
        管理员用户对象
        
    Raises:
        PermissionError: 用户不是管理员时抛出
    """
    if current_user.role != UserRole.ADMIN:
        raise PermissionError("需要管理员权限")
    
    return current_user


def require_user_or_admin(
    target_user_id: int,
    current_user: User = Depends(get_current_user)
) -> User:
    """
    用户本人或管理员权限依赖
    
    Args:
        target_user_id: 目标用户ID
        current_user: 当前用户
        
    Returns:
        有权限的用户对象
        
    Raises:
        PermissionError: 权限不足时抛出
    """
    if current_user.role != UserRole.ADMIN and current_user.id != target_user_id:
        raise PermissionError("只能操作自己的信息或需要管理员权限")
    
    return current_user


class TokenBlacklist:
    """Token黑名单管理"""
    
    @staticmethod
    def add_token(token: str) -> None:
        """将token添加到黑名单"""
        _token_blacklist.add(token)
        logger.info(f"Token已添加到黑名单")
    
    @staticmethod
    def remove_token(token: str) -> None:
        """从黑名单中移除token"""
        _token_blacklist.discard(token)
        logger.info(f"Token已从黑名单移除")
    
    @staticmethod
    def is_blacklisted(token: str) -> bool:
        """检查token是否在黑名单中"""
        return token in _token_blacklist
    
    @staticmethod
    def clear_blacklist() -> None:
        """清空黑名单"""
        _token_blacklist.clear()
        logger.info("Token黑名单已清空")
    
    @staticmethod
    def get_blacklist_size() -> int:
        """获取黑名单大小"""
        return len(_token_blacklist)


# 可选的用户依赖（不强制要求认证）
async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    获取可选的当前用户（不强制要求认证）
    
    Args:
        credentials: 可选的HTTP Bearer 凭证
        db: 数据库会话
        
    Returns:
        用户对象或None
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        
        # 检查token是否在黑名单中
        if token in _token_blacklist:
            return None
        
        # 验证token
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        if not user_id:
            return None
        
        user = user_service.get(db, int(user_id))
        
        if not user or not user.is_active:
            return None
        
        return user
        
    except Exception as e:
        logger.debug(f"可选用户认证失败: {str(e)}")
        return None


# 导出常用的依赖
__all__ = [
    "get_current_user_id",
    "get_current_user", 
    "get_current_active_user",
    "get_optional_current_user",
    "require_admin",
    "require_user_or_admin",
    "require_roles",
    "TokenBlacklist",
    "AuthenticationError",
    "PermissionError"
]
