"""
系统配置API端点

提供系统配置的查询、更新等功能
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.system_config_service import SystemConfigService
from app.services.user_service import UserService
from app.schemas.system_config import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse
)
from app.schemas.common import ResponseModel, PaginatedResponse
from app.utils.security import verify_token
from app.models.user import UserRole

router = APIRouter()
security = HTTPBearer()
config_service = SystemConfigService()
user_service = UserService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户对象"""
    payload = verify_token(credentials.credentials)
    user_id = int(payload.get("sub"))
    
    user = user_service.get(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.get("/public", response_model=ResponseModel[Dict[str, Any]], summary="获取公开配置")
async def get_public_configs(
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    获取所有公开的系统配置
    
    不需要认证，用于前端获取公开配置信息
    """
    try:
        configs = config_service.get_public_configs(db)
        
        return ResponseModel(
            code=200,
            message="获取公开配置成功",
            data=configs
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公开配置失败: {str(e)}"
        )


@router.get("/configs", response_model=ResponseModel[PaginatedResponse[SystemConfigResponse]], summary="获取配置列表")
async def get_configs(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    category: Optional[str] = Query(None, description="配置分类筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（配置键、描述）"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[PaginatedResponse[SystemConfigResponse]]:
    """
    获取系统配置列表（需要管理员权限）
    
    - **page**: 页码，从1开始
    - **size**: 每页大小，最大100
    - **category**: 配置分类筛选（可选）
    - **search**: 搜索关键词（可选）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 构建筛选条件
        filters = {}
        if category:
            filters["category"] = category
        
        # 获取配置列表
        if search:
            configs, total = config_service.search_configs(
                db, search_term=search, skip=(page - 1) * size, limit=size, **filters
            )
        else:
            configs, total = config_service.get_multi_with_total(
                db, skip=(page - 1) * size, limit=size, **filters
            )
        
        # 转换为响应模型
        config_responses = [SystemConfigResponse.model_validate(config) for config in configs]
        
        # 构建分页响应
        pages = (total + size - 1) // size
        paginated_data = PaginatedResponse(
            items=config_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
        return ResponseModel(
            code=200,
            message="获取配置列表成功",
            data=paginated_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置列表失败: {str(e)}"
        )


@router.get("/categories", response_model=ResponseModel[List[str]], summary="获取配置分类列表")
async def get_config_categories(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[List[str]]:
    """
    获取所有配置分类列表（需要管理员权限）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        categories = config_service.get_categories(db)
        
        return ResponseModel(
            code=200,
            message="获取配置分类成功",
            data=categories
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置分类失败: {str(e)}"
        )


@router.post("/configs", response_model=ResponseModel[SystemConfigResponse], summary="创建配置")
async def create_config(
    config_data: SystemConfigCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SystemConfigResponse]:
    """
    创建新的系统配置（需要管理员权限）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查配置键是否已存在
        existing_config = config_service.get_by_key(db, config_data.config_key)
        if existing_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置键已存在"
            )

        # 创建配置
        config = config_service.create(db, obj_in=config_data)
        
        return ResponseModel(
            code=200,
            message="创建配置成功",
            data=SystemConfigResponse.model_validate(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建配置失败: {str(e)}"
        )


@router.get("/configs/{config_id}", response_model=ResponseModel[SystemConfigResponse], summary="获取配置详情")
async def get_config(
    config_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SystemConfigResponse]:
    """
    获取指定配置的详细信息（需要管理员权限）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        config = config_service.get(db, config_id)
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置不存在"
            )
        
        return ResponseModel(
            code=200,
            message="获取配置详情成功",
            data=SystemConfigResponse.model_validate(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置详情失败: {str(e)}"
        )


@router.put("/configs/{config_id}", response_model=ResponseModel[SystemConfigResponse], summary="更新配置")
async def update_config(
    config_id: int,
    config_data: SystemConfigUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[SystemConfigResponse]:
    """
    更新系统配置（需要管理员权限）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查配置是否存在
        existing_config = config_service.get(db, config_id)
        if not existing_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置不存在"
            )

        # 更新配置
        config = config_service.update(db, db_obj=existing_config, obj_in=config_data)
        
        return ResponseModel(
            code=200,
            message="更新配置成功",
            data=SystemConfigResponse.model_validate(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置失败: {str(e)}"
        )


@router.delete("/configs/{config_id}", response_model=ResponseModel[None], summary="删除配置")
async def delete_config(
    config_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    删除系统配置（需要管理员权限）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查配置是否存在
        config = config_service.get(db, config_id)
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置不存在"
            )

        # 删除配置
        config_service.remove(db, id=config_id)
        
        return ResponseModel(
            code=200,
            message="删除配置成功",
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除配置失败: {str(e)}"
        )


@router.post("/configs/batch-update", response_model=ResponseModel[List[SystemConfigResponse]], summary="批量更新配置")
async def batch_update_configs(
    updates: List[Dict[str, Any]],
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[List[SystemConfigResponse]]:
    """
    批量更新系统配置（需要管理员权限）
    
    - **updates**: 更新列表，每个元素包含 {"key": "配置键", "value": "新值"}
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 批量更新配置
        updated_configs = config_service.batch_update_by_keys(db, updates)
        config_responses = [SystemConfigResponse.model_validate(config) for config in updated_configs]
        
        return ResponseModel(
            code=200,
            message="批量更新配置成功",
            data=config_responses
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新配置失败: {str(e)}"
        )


@router.post("/configs/refresh-cache", response_model=ResponseModel[None], summary="刷新配置缓存")
async def refresh_config_cache(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    刷新配置缓存（需要管理员权限）
    """
    # 检查权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 刷新缓存
        config_service.refresh_cache(db)
        
        return ResponseModel(
            code=200,
            message="刷新配置缓存成功",
            data=None
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"刷新配置缓存失败: {str(e)}"
        )
