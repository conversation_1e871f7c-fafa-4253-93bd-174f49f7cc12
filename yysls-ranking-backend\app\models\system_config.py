"""
系统配置模型
"""
from datetime import datetime
from enum import Enum

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String, Text

from app.core.database import Base


class ConfigType(str, Enum):
    """配置类型枚举"""
    BROADCAST = "broadcast"      # 播报配置
    SPONSOR = "sponsor"          # 赞助商配置
    ADVERTISEMENT = "advertisement"  # 广告配置
    SYSTEM = "system"           # 系统配置
    UI = "ui"                   # 界面配置


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"

    id = Column(Integer, primary_key=True, index=True)
    
    # 配置信息
    config_key = Column(String(100), unique=True, nullable=False, comment="配置键")
    config_value = Column(Text, nullable=True, comment="配置值")
    config_type = Column(String(50), nullable=False, comment="配置类型")
    
    # 描述信息
    name = Column(String(200), nullable=False, comment="配置名称")
    description = Column(Text, nullable=True, comment="配置描述")
    
    # 数据类型和验证
    value_type = Column(String(20), default="string", nullable=False, comment="值类型")
    default_value = Column(Text, nullable=True, comment="默认值")
    validation_rule = Column(Text, nullable=True, comment="验证规则")
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    is_public = Column(Boolean, default=False, nullable=False, comment="是否公开")
    
    # 排序和分组
    group_name = Column(String(100), nullable=True, comment="分组名称")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    def __repr__(self):
        return f"<SystemConfig(id={self.id}, key='{self.config_key}', type='{self.config_type}')>"
