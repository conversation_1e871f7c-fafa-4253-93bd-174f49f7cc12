"""
系统配置相关的Pydantic模型
"""
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, Field, ConfigDict


class SystemConfigBase(BaseModel):
    """系统配置基础模型"""
    config_key: str = Field(..., description="配置键", max_length=100)
    config_value: Optional[str] = Field(None, description="配置值")
    config_type: str = Field(..., description="配置类型", max_length=50)
    name: str = Field(..., description="配置名称", max_length=200)
    description: Optional[str] = Field(None, description="配置描述")
    value_type: str = Field("string", description="值类型", max_length=20)
    default_value: Optional[str] = Field(None, description="默认值")
    validation_rule: Optional[str] = Field(None, description="验证规则")
    is_active: bool = Field(True, description="是否启用")
    is_public: bool = Field(False, description="是否公开")
    group_name: Optional[str] = Field(None, description="分组名称", max_length=100)
    sort_order: int = Field(0, description="排序")


class SystemConfigCreate(SystemConfigBase):
    """创建系统配置的请求模型"""
    pass


class SystemConfigUpdate(BaseModel):
    """更新系统配置的请求模型"""
    config_value: Optional[str] = Field(None, description="配置值")
    config_type: Optional[str] = Field(None, description="配置类型", max_length=50)
    name: Optional[str] = Field(None, description="配置名称", max_length=200)
    description: Optional[str] = Field(None, description="配置描述")
    value_type: Optional[str] = Field(None, description="值类型", max_length=20)
    default_value: Optional[str] = Field(None, description="默认值")
    validation_rule: Optional[str] = Field(None, description="验证规则")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_public: Optional[bool] = Field(None, description="是否公开")
    group_name: Optional[str] = Field(None, description="分组名称", max_length=100)
    sort_order: Optional[int] = Field(None, description="排序")


class SystemConfigResponse(SystemConfigBase):
    """系统配置响应模型"""
    id: int = Field(..., description="配置ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class SystemConfigPublic(BaseModel):
    """公开系统配置模型（不需要认证）"""
    config_key: str = Field(..., description="配置键")
    config_value: Optional[str] = Field(None, description="配置值")
    config_type: str = Field(..., description="配置类型")
    name: str = Field(..., description="配置名称")
    description: Optional[str] = Field(None, description="配置描述")

    model_config = ConfigDict(from_attributes=True)


class ConfigBatchUpdate(BaseModel):
    """批量更新配置的请求模型"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
